import os
from typing import Any, List, Optional
from langchain.llms.base import LLM
from langchain_core.embeddings import Embeddings
from azure.ai.inference import ChatCompletionsClient
from azure.ai.inference.models import SystemMessage, UserMessage
from azure.core.credentials import AzureKeyCredential

class GitHubModelsLLM(LLM):
    """Custom LLM wrapper for GitHub Models using Azure AI Inference."""

    model: str = "gpt-4o-mini"
    temperature: float = 0.0
    endpoint: str = "https://models.inference.ai.azure.com"
    client: Optional[ChatCompletionsClient] = None

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        token = os.environ.get("GITHUB_TOKEN")
        if not token:
            raise ValueError("GITHUB_TOKEN environment variable must be set")

        self.client = ChatCompletionsClient(
            endpoint=self.endpoint,
            credential=AzureKeyCredential(token),
        )
    
    @property
    def _llm_type(self) -> str:
        return "github_models"
    
    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[Any] = None,
        **kwargs: Any,
    ) -> str:
        """Call the GitHub Models API."""
        try:
            response = self.client.complete(
                messages=[
                    UserMessage(content=prompt)
                ],
                temperature=self.temperature,
                model=self.model,
                **kwargs
            )
            return response.choices[0].message.content
        except Exception as e:
            return f"Error calling GitHub Models: {str(e)}"

class GitHubModelsEmbeddings(Embeddings):
    """Custom embeddings wrapper for GitHub Models."""

    def __init__(self):
        super().__init__()
        self.endpoint = "https://models.inference.ai.azure.com"
        token = os.environ.get("GITHUB_TOKEN")
        if not token:
            raise ValueError("GITHUB_TOKEN environment variable must be set")

        self.client = ChatCompletionsClient(
            endpoint=self.endpoint,
            credential=AzureKeyCredential(token),
        )
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Embed a list of documents. For now, we'll use a simple approach."""
        # Note: GitHub Models doesn't have a direct embeddings API yet
        # This is a placeholder - in practice you might want to use a different service
        # or implement text-to-vector conversion differently
        embeddings = []
        for text in texts:
            # Simple hash-based embedding (not ideal for production)
            embedding = [float(hash(text[i:i+10]) % 1000) / 1000.0 for i in range(0, min(len(text), 1536), 10)]
            # Pad or truncate to standard embedding size
            if len(embedding) < 1536:
                embedding.extend([0.0] * (1536 - len(embedding)))
            else:
                embedding = embedding[:1536]
            embeddings.append(embedding)
        return embeddings
    
    def embed_query(self, text: str) -> List[float]:
        """Embed a single query."""
        return self.embed_documents([text])[0]