import warnings
warnings.filterwarnings("ignore", category=FutureWarning)
from mcp_agents.document_mcp_agent import DocumentMCPAgent
from mcp_agents.web_mcp_agent import WebMCPAgent
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

def route_question(question: str, doc_agent, web_agent) -> str:
    # Simple routing logic based on keywords
    keywords_for_web = ["latest", "current", "news", "update", "search", "google", "web", "president", "prime minister", "time"]
    if any(keyword in question.lower() for keyword in keywords_for_web):
        agent = web_agent
    else:
        agent = doc_agent

    response = agent.handle_request({"question": question})
    return response.get("answer", "Sorry, I couldn't get an answer.")

def main():
    # Environment variables are loaded from .env file
    # Verify that required environment variables are set
    if not os.getenv("GITHUB_TOKEN"):
        print("Error: GITHUB_TOKEN environment variable is not set")
        return
    
    if not os.getenv("GOOGLE_API_KEY"):
        print("Warning: GOOGLE_API_KEY not set - web search functionality will be limited")
    
    if not os.getenv("GOOGLE_CSE_ID"):
        print("Warning: GOOGLE_CSE_ID not set - web search functionality will be limited")

    # Initialize agents
    print("Initializing agents...")
    doc_agent = DocumentMCPAgent("data/sample_policy_and_procedures_manual.pdf")
    web_agent = WebMCPAgent()
    print("Agents initialized successfully!")

    print("Welcome to the MCP 2-Agent Smart Chatbot!")
    print("Type 'exit' or 'quit' to stop.\n")

    while True:
        user_input = input("You: ")
        if user_input.strip().lower() in ["exit", "quit"]:
            print("Goodbye!")
            break

        answer = route_question(user_input, doc_agent, web_agent)
        print(f"Bot: {answer}")

if __name__ == "__main__":
    main()
