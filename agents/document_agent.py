from langchain_community.document_loaders import PyPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import FAISS
from langchain.chains import RetrievalQA
from langchain_huggingface import HuggingFaceEmbeddings
from github_models_llm import GitHubModelsLLM
import os

class DocumentAgent:
    def __init__(self, pdf_path):
        self.pdf_path = pdf_path
        self._load_documents()
        self._build_vector_store()
        self._build_qa_chain()

    def _load_documents(self):
        loader = PyPDFLoader(self.pdf_path)
        self.documents = loader.load()

    def _build_vector_store(self):
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
        texts = text_splitter.split_documents(self.documents)
        # Use HuggingFace embeddings for better semantic similarity
        embeddings = HuggingFaceEmbeddings(model_name="all-MiniLM-L6-v2")
        self.vectorstore = FAISS.from_documents(texts, embeddings)

    def _build_qa_chain(self):
        llm = GitHubModelsLLM(temperature=0)
        retriever = self.vectorstore.as_retriever()
        self.qa_chain = RetrievalQA.from_chain_type(llm=llm, retriever=retriever)

    def answer_question(self, question: str) -> str:
        return self.qa_chain.invoke({"query": question})["result"]
