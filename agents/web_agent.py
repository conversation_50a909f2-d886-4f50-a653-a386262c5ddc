import os
import requests
from github_models_llm import GitHubModelsLLM
import json

class WebAgent:
    def __init__(self):
        self.api_key = os.getenv("GOOGLE_API_KEY")
        self.cse_id = os.getenv("GOOGLE_CSE_ID")
        self.llm = GitHubModelsLLM(temperature=0)

    def google_search(self, query, num_results=3):
        # Check if API credentials are available
        if not self.api_key or not self.cse_id:
            return [], "Google API credentials not configured. Please set GOOGLE_API_KEY and GOOGLE_CSE_ID environment variables."

        url = "https://www.googleapis.com/customsearch/v1"
        params = {
            "key": self.api_key,
            "cx": self.cse_id,
            "q": query,
            "num": num_results,
        }

        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()  # Raise an exception for bad status codes

            results = response.json()

            # Check for API errors
            if "error" in results:
                error_msg = results["error"].get("message", "Unknown API error")
                return [], f"Google API error: {error_msg}"

            snippets = []
            for item in results.get("items", []):
                snippet = item.get("snippet")
                if snippet:
                    snippets.append(snippet)

            if not snippets:
                return [], "No search results found for your query."

            return snippets, None

        except requests.exceptions.Timeout:
            return [], "Search request timed out. Please try again."
        except requests.exceptions.RequestException as e:
            return [], f"Network error during search: {str(e)}"
        except Exception as e:
            return [], f"Unexpected error during search: {str(e)}"

    def fallback_search(self, query):
        """Fallback search using basic knowledge base for common questions"""
        query_lower = query.lower()

        # Basic knowledge base for common questions
        knowledge_base = {
            "president": [
                "As of 2024, Joe Biden is the current President of the United States.",
                "Joe Biden became the 46th President of the United States in January 2021.",
                "The current U.S. President serves a four-year term and can be re-elected for one additional term."
            ],
            "capital": {
                "france": ["Paris is the capital and largest city of France.", "Paris is located in northern France and is known for landmarks like the Eiffel Tower."],
                "usa": ["Washington, D.C. is the capital of the United States.", "Washington D.C. stands for District of Columbia."],
                "uk": ["London is the capital of the United Kingdom.", "London is also the largest city in the UK."],
                "germany": ["Berlin is the capital of Germany.", "Berlin became the capital after German reunification."],
                "japan": ["Tokyo is the capital of Japan.", "Tokyo is one of the world's most populous metropolitan areas."],
                "china": ["Beijing is the capital of China.", "Beijing is also known as Peking in some contexts."],
                "india": ["New Delhi is the capital of India.", "New Delhi is part of the larger Delhi metropolitan area."],
                "australia": ["Canberra is the capital of Australia.", "Many people mistakenly think Sydney or Melbourne is the capital."],
                "canada": ["Ottawa is the capital of Canada.", "Ottawa is located in the province of Ontario."],
                "brazil": ["Brasília is the capital of Brazil.", "Brasília was purpose-built as the capital in the 1960s."]
            },
            "weather": [
                "I cannot provide real-time weather information as I don't have access to current weather data.",
                "For current weather information, please check a weather service like weather.com or your local weather app.",
                "Weather conditions change frequently and require real-time data sources."
            ],
            "time": [
                "I cannot provide the current time as I don't have access to real-time data.",
                "Please check your device's clock or search for 'current time' in your preferred search engine.",
                "Time zones vary around the world, so the current time depends on your location."
            ],
            "ai": [
                "Artificial Intelligence (AI) is a rapidly evolving field focused on creating intelligent machines.",
                "Recent developments in AI include large language models, computer vision, and machine learning advances.",
                "AI applications span many industries including healthcare, finance, transportation, and technology."
            ]
        }

        # Check for president-related questions
        if any(word in query_lower for word in ["president", "biden", "trump"]):
            return knowledge_base["president"], None

        # Check for capital city questions
        if "capital" in query_lower:
            for country, info in knowledge_base["capital"].items():
                if country in query_lower:
                    return info, None
            return ["I can help with capital cities of major countries. Try asking about France, USA, UK, Germany, Japan, China, India, Australia, Canada, or Brazil."], None

        # Check for weather questions
        if any(word in query_lower for word in ["weather", "temperature", "rain", "snow", "sunny"]):
            return knowledge_base["weather"], None

        # Check for time questions
        if any(word in query_lower for word in ["time", "clock", "hour", "minute"]):
            return knowledge_base["time"], None

        # Check for AI questions
        if any(word in query_lower for word in ["artificial intelligence", "ai", "machine learning", "ml"]):
            return knowledge_base["ai"], None

        # Default fallback
        return [
            "I don't have access to real-time web search at the moment.",
            "This could be due to API configuration issues or network connectivity.",
            "For current information, please try using a web browser to search for your question.",
            "I can still help answer questions about the document or provide general knowledge on common topics."
        ], None

    def answer_question(self, question: str) -> str:
        # Try Google Search first
        snippets, error = self.google_search(question)

        # If Google search fails, try fallback search
        if error and "credentials not configured" in error.lower():
            print("Google API not configured, using fallback search...")
            snippets, fallback_error = self.fallback_search(question)
            if fallback_error:
                return f"Web search failed: Google API not configured and fallback search failed: {fallback_error}"
        elif error:
            # Try fallback for other Google API errors too
            print(f"Google search failed ({error}), trying fallback...")
            snippets, fallback_error = self.fallback_search(question)
            if fallback_error:
                return f"Web search failed: {error}. Fallback search also failed: {fallback_error}"

        if not snippets:
            return "Sorry, I couldn't find relevant information on the web. Please check your internet connection or try a different question."

        # Combine snippets for LLM input
        context = "\n\n".join(snippets)
        prompt = f"Answer the question based on the following web search results:\n\n{context}\n\nQuestion: {question}\nAnswer:"

        try:
            answer = self.llm(prompt)
            return answer
        except Exception as e:
            return f"Error generating answer: {str(e)}"
