import os
import requests
from github_models_llm import GitHubModelsLLM

class WebAgent:
    def __init__(self):
        self.api_key = os.getenv("GOOGLE_API_KEY")
        self.cse_id = os.getenv("GOOGLE_CSE_ID")
        self.llm = GitHubModelsLLM(temperature=0)

    def google_search(self, query, num_results=3):
        url = "https://www.googleapis.com/customsearch/v1"
        params = {
            "key": self.api_key,
            "cx": self.cse_id,
            "q": query,
            "num": num_results,
        }
        response = requests.get(url, params=params)
        results = response.json()
        snippets = []
        for item in results.get("items", []):
            snippet = item.get("snippet")
            if snippet:
                snippets.append(snippet)
        return snippets

    def answer_question(self, question: str) -> str:
        snippets = self.google_search(question)
        if not snippets:
            return "Sorry, I couldn't find relevant information on the web."

        # Combine snippets for LLM input
        context = "\n\n".join(snippets)
        prompt = f"Answer the question based on the following web search results:\n\n{context}\n\nQuestion: {question}\nAnswer:"
        answer = self.llm(prompt)
        return answer
