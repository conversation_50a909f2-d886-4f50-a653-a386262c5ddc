import streamlit as st
import warnings
import os
from dotenv import load_dotenv
from mcp_agents.document_mcp_agent import DocumentMCPAgent
from mcp_agents.web_mcp_agent import WebMCPAgent

# Suppress warnings
warnings.filterwarnings("ignore", category=FutureWarning)

# Load environment variables
load_dotenv()

# Page configuration
st.set_page_config(
    page_title="MCP Smart Chatbot",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

def check_environment_variables():
    """Check if required environment variables are set"""
    issues = []
    
    if not os.getenv("GITHUB_TOKEN"):
        issues.append("❌ GITHUB_TOKEN is not set")
    else:
        issues.append("✅ GITHUB_TOKEN is configured")
    
    if not os.getenv("GOOGLE_API_KEY"):
        issues.append("⚠️ GOOGLE_API_KEY is not set - web search will be limited")
    else:
        issues.append("✅ GOOGLE_API_KEY is configured")
    
    if not os.getenv("GOOGLE_CSE_ID"):
        issues.append("⚠️ GOOGLE_CSE_ID is not set - web search will be limited")
    else:
        issues.append("✅ GOOGLE_CSE_ID is configured")
    
    return issues

@st.cache_resource
def initialize_agents():
    """Initialize the agents with caching to avoid reloading"""
    try:
        doc_agent = DocumentMCPAgent("data/sample_policy_and_procedures_manual.pdf")
        web_agent = WebMCPAgent()
        return doc_agent, web_agent, None
    except Exception as e:
        return None, None, str(e)

def route_question(question: str, doc_agent, web_agent) -> tuple[str, str]:
    """Route question to appropriate agent and return response with agent type"""
    # Simple routing logic based on keywords
    keywords_for_web = ["latest", "current", "news", "update", "search", "google", "web", "president", "prime minister", "time"]
    
    if any(keyword in question.lower() for keyword in keywords_for_web):
        agent = web_agent
        agent_type = "🌐 Web Agent"
    else:
        agent = doc_agent
        agent_type = "📄 Document Agent"
    
    try:
        response = agent.handle_request({"question": question})
        return response.get("answer", "Sorry, I couldn't get an answer."), agent_type
    except Exception as e:
        return f"Error: {str(e)}", agent_type

def main():
    # Header
    st.title("🤖 MCP Smart Chatbot")
    st.markdown("Ask questions and get answers from documents or the web!")
    
    # Sidebar for configuration and info
    with st.sidebar:
        st.header("🔧 Configuration")
        
        # Environment variables status
        st.subheader("Environment Status")
        env_issues = check_environment_variables()
        for issue in env_issues:
            st.write(issue)
        
        st.divider()
        
        # Agent information
        st.subheader("🤖 Available Agents")
        st.write("**📄 Document Agent**")
        st.write("- Answers questions from the PDF document")
        st.write("- Uses RAG (Retrieval Augmented Generation)")
        st.write("- Best for policy and procedure questions")
        
        st.write("**🌐 Web Agent**")
        st.write("- Searches the web for current information")
        st.write("- Uses Google Custom Search API")
        st.write("- Best for latest news and updates")
        
        st.divider()
        
        # Routing keywords
        st.subheader("🎯 Auto-routing Keywords")
        st.write("Questions containing these words go to Web Agent:")
        st.code("latest, current, news, update, search, google, web, president, prime minister, time")
        st.write("All other questions go to Document Agent")
        
        st.divider()
        
        # Clear chat button
        if st.button("🗑️ Clear Chat History"):
            st.session_state.messages = []
            st.rerun()
    
    # Initialize agents
    if 'agents_initialized' not in st.session_state:
        with st.spinner("Initializing agents..."):
            doc_agent, web_agent, error = initialize_agents()
            
            if error:
                st.error(f"Failed to initialize agents: {error}")
                st.stop()
            
            st.session_state.doc_agent = doc_agent
            st.session_state.web_agent = web_agent
            st.session_state.agents_initialized = True
        
        st.success("✅ Agents initialized successfully!")
    
    # Initialize chat history
    if "messages" not in st.session_state:
        st.session_state.messages = []
    
    # Display chat messages from history on app rerun
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            if message["role"] == "assistant" and "agent_type" in message:
                st.write(f"*Answered by: {message['agent_type']}*")
            st.markdown(message["content"])
    
    # Chat input
    if prompt := st.chat_input("Ask me anything about the document or current events..."):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})
        
        # Display user message
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # Get response from appropriate agent
        with st.chat_message("assistant"):
            with st.spinner("Thinking..."):
                answer, agent_type = route_question(
                    prompt, 
                    st.session_state.doc_agent, 
                    st.session_state.web_agent
                )
            
            st.write(f"*Answered by: {agent_type}*")
            st.markdown(answer)
        
        # Add assistant response to chat history
        st.session_state.messages.append({
            "role": "assistant", 
            "content": answer,
            "agent_type": agent_type
        })

if __name__ == "__main__":
    main()
