#!/usr/bin/env python3
"""
Test script for the Web Agent to debug search functionality
"""
import os
from dotenv import load_dotenv
from agents.web_agent import WebAgent

def test_web_agent():
    print("🧪 Testing Web Agent...")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Check environment variables
    google_api_key = os.getenv("GOOGLE_API_KEY")
    google_cse_id = os.getenv("GOOGLE_CSE_ID")
    github_token = os.getenv("GITHUB_TOKEN")
    
    print(f"GITHUB_TOKEN: {'✅ Set' if github_token else '❌ Not set'}")
    print(f"GOOGLE_API_KEY: {'✅ Set' if google_api_key else '❌ Not set'}")
    print(f"GOOGLE_CSE_ID: {'✅ Set' if google_cse_id else '❌ Not set'}")
    print()
    
    if not github_token:
        print("❌ Cannot test without GITHUB_TOKEN")
        return
    
    # Initialize web agent
    try:
        web_agent = WebAgent()
        print("✅ Web Agent initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize Web Agent: {e}")
        return
    
    # Test questions
    test_questions = [
        "Who is the current president of USA?",
        "What is the capital of France?",
        "Current weather in New York",
        "Latest news about artificial intelligence"
    ]
    
    for question in test_questions:
        print(f"\n🔍 Testing: '{question}'")
        print("-" * 40)
        
        try:
            answer = web_agent.answer_question(question)
            print(f"Answer: {answer}")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    test_web_agent()
