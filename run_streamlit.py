#!/usr/bin/env python3
"""
Simple script to run the Streamlit app with proper environment setup
"""
import subprocess
import sys
import os

def main():
    # Check if we're in a virtual environment
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("Warning: Not running in a virtual environment")
        print("Consider activating your virtual environment first:")
        print("source venv/bin/activate")
        print()
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("Warning: .env file not found")
        print("Make sure to create a .env file with your API keys:")
        print("GITHUB_TOKEN=your_github_token")
        print("GOOGLE_API_KEY=your_google_api_key")
        print("GOOGLE_CSE_ID=your_google_cse_id")
        print()
    
    # Run Streamlit
    try:
        print("Starting Streamlit app...")
        subprocess.run([sys.executable, "-m", "streamlit", "run", "streamlit_app.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"Error running Streamlit: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\nStreamlit app stopped by user")

if __name__ == "__main__":
    main()
