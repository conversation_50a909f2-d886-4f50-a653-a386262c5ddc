#!/usr/bin/env python3
"""
Demo script to showcase the MCP Smart Chatbot functionality
"""
import os
import sys
from dotenv import load_dotenv

def check_setup():
    """Check if the environment is properly set up"""
    print("🔍 Checking MCP Smart Chatbot Setup...")
    print("=" * 50)
    
    # Check virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Virtual environment: Active")
    else:
        print("⚠️  Virtual environment: Not detected")
    
    # Load environment variables
    load_dotenv()
    
    # Check environment variables
    github_token = os.getenv("GITHUB_TOKEN")
    google_api_key = os.getenv("GOOGLE_API_KEY")
    google_cse_id = os.getenv("GOOGLE_CSE_ID")
    
    print(f"✅ GITHUB_TOKEN: {'Set' if github_token else '❌ Not set'}")
    print(f"{'✅' if google_api_key else '⚠️ '} GOOGLE_API_KEY: {'Set' if google_api_key else 'Not set (web search limited)'}")
    print(f"{'✅' if google_cse_id else '⚠️ '} GOOGLE_CSE_ID: {'Set' if google_cse_id else 'Not set (web search limited)'}")
    
    # Check PDF file
    pdf_path = "data/sample_policy_and_procedures_manual.pdf"
    if os.path.exists(pdf_path):
        print(f"✅ PDF Document: Found at {pdf_path}")
    else:
        print(f"❌ PDF Document: Not found at {pdf_path}")
    
    print("\n" + "=" * 50)
    
    # Check required packages
    try:
        import streamlit
        print(f"✅ Streamlit: {streamlit.__version__}")
    except ImportError:
        print("❌ Streamlit: Not installed")
    
    try:
        import langchain
        print(f"✅ LangChain: {langchain.__version__}")
    except ImportError:
        print("❌ LangChain: Not installed")
    
    try:
        import faiss
        print("✅ FAISS: Available")
    except ImportError:
        print("❌ FAISS: Not installed")
    
    print("\n" + "=" * 50)
    return github_token is not None

def show_usage():
    """Show usage instructions"""
    print("\n🚀 How to Run the MCP Smart Chatbot:")
    print("=" * 50)
    print("1. Streamlit Web Interface (Recommended):")
    print("   python run_streamlit.py")
    print("   OR")
    print("   streamlit run streamlit_app.py")
    print()
    print("2. Command Line Interface:")
    print("   python main.py")
    print()
    print("📖 The web interface will open at: http://localhost:8501")
    print()
    print("💡 Example Questions:")
    print("   - 'What is the company policy on remote work?' (Document Agent)")
    print("   - 'What is the latest news about AI?' (Web Agent)")
    print("   - 'Current weather in New York' (Web Agent)")
    print("   - 'How do I submit a vacation request?' (Document Agent)")

def main():
    print("🤖 MCP Smart Chatbot Demo")
    print("=" * 50)
    
    setup_ok = check_setup()
    
    if not setup_ok:
        print("\n❌ Setup incomplete!")
        print("Please ensure GITHUB_TOKEN is set in your .env file")
        print("Copy .env.example to .env and fill in your API keys")
        return
    
    show_usage()
    
    print("\n" + "=" * 50)
    print("Ready to go! 🎉")

if __name__ == "__main__":
    main()
