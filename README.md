# MCP Smart Chatbot

A multi-agent chatbot system that can answer questions from documents or search the web for current information.

## Features

- **Document Agent**: Answers questions based on PDF documents using RAG (Retrieval Augmented Generation)
- **Web Agent**: Searches the web for current information using Google Custom Search API
- **Smart Routing**: Automatically routes questions to the appropriate agent based on keywords
- **Streamlit Frontend**: User-friendly web interface
- **Command Line Interface**: Traditional CLI for quick interactions

## Setup

### 1. Environment Setup

Create a virtual environment and install dependencies:

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install streamlit langchain langchain-community langchain-huggingface faiss-cpu azure-ai-inference python-dotenv requests
```

### 2. Environment Variables

Create a `.env` file in the project root with your API keys:

```env
GITHUB_TOKEN=your_github_token_here
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_CSE_ID=your_google_cse_id_here
```

**Required:**
- `GITHUB_TOKEN`: GitHub personal access token for Azure AI Inference

**Optional (for web search):**
- `GOOGLE_API_KEY`: Google Cloud API key with Custom Search API enabled
- `GOOGLE_CSE_ID`: Google Custom Search Engine ID

### 3. Document Setup

Place your PDF document in the `data/` directory. The default document is `sample_policy_and_procedures_manual.pdf`.

## Usage

### Streamlit Web Interface (Recommended)

Run the Streamlit app:

```bash
# Using the helper script
python run_streamlit.py

# Or directly
streamlit run streamlit_app.py
```

The web interface will open in your browser at `http://localhost:8501`.

### Command Line Interface

Run the CLI version:

```bash
python main.py
```

## How It Works

### Agent Routing

The system automatically routes questions to the appropriate agent:

**Web Agent** (for current/live information):
- Questions containing: "latest", "current", "news", "update", "search", "google", "web", "president", "prime minister", "time"
- Examples: "What's the latest news?", "Current weather", "Who is the president?"

**Document Agent** (for document-based questions):
- All other questions
- Examples: "What is the company policy?", "How do I submit a request?"

### Architecture

```
User Question
     ↓
Question Router
     ↓
┌─────────────────┬─────────────────┐
│  Document Agent │   Web Agent     │
│                 │                 │
│  PDF Document   │  Google Search  │
│  + RAG          │  + LLM          │
│  + Vector Store │                 │
└─────────────────┴─────────────────┘
     ↓
  Response to User
```

## Components

- **`streamlit_app.py`**: Web interface using Streamlit
- **`main.py`**: Command-line interface
- **`agents/document_agent.py`**: PDF document processing and Q&A
- **`agents/web_agent.py`**: Web search and answer generation
- **`mcp_agents/`**: MCP protocol wrappers for the agents
- **`github_models_llm.py`**: Custom LLM wrapper for GitHub Models

## Features of the Streamlit Interface

- **Chat Interface**: Natural conversation flow with message history
- **Agent Identification**: Shows which agent answered each question
- **Environment Status**: Displays configuration status in sidebar
- **Auto-routing Info**: Shows routing keywords and logic
- **Clear History**: Button to reset conversation
- **Error Handling**: Graceful error messages and status indicators

## Troubleshooting

### Common Issues

1. **"GITHUB_TOKEN environment variable is not set"**
   - Make sure your `.env` file contains a valid GitHub token
   - Ensure the `.env` file is in the project root directory

2. **Web search not working**
   - Check that `GOOGLE_API_KEY` and `GOOGLE_CSE_ID` are set in `.env`
   - Verify your Google Cloud project has Custom Search API enabled

3. **PDF not loading**
   - Ensure the PDF file exists in the `data/` directory
   - Check file permissions and path

4. **Streamlit not starting**
   - Make sure Streamlit is installed: `pip install streamlit`
   - Check that you're in the correct directory
   - Verify virtual environment is activated

### Performance Tips

- The document agent initializes once and caches the vector store
- Large PDF files may take longer to process initially
- Web searches are limited by Google API quotas

## License

This project is for demonstration purposes.
